import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        // Syrian Ministry of Defense Color Palette
        'deep-charcoal': '#1a1a1a',
        'military-black': '#0d0d0d',
        'steel-gray': '#2d2d2d',
        'smoke-gray': '#404040',
        'military-green': '#2d5016',
        'bright-green': '#4a7c59',
        'syrian-flag-green': '#007a3d',
        'warning-red': '#8b0000',
        'pure-white': '#ffffff',
        'light-gray': '#cccccc',
        'muted-gray': '#999999',
        
        // Semantic colors
        background: 'var(--background)',
        foreground: 'var(--foreground)',
      },
      fontFamily: {
        // Arabic fonts
        'arabic-primary': ['Amiri', 'serif'],
        'arabic-secondary': ['Noto Sans Arabic', 'sans-serif'],
        'arabic-headings': ['Cairo', 'sans-serif'],
        
        // English fonts
        'english-primary': ['Inter', 'sans-serif'],
        'english-secondary': ['Inter', 'sans-serif'],
        'mono': ['JetBrains Mono', 'monospace'],
      },
      fontSize: {
        'hero': ['3rem', { lineHeight: '1.1', letterSpacing: '-0.02em' }],
        'section': ['2.25rem', { lineHeight: '1.2', letterSpacing: '-0.01em' }],
        'subsection': ['1.5rem', { lineHeight: '1.3' }],
        'body': ['1rem', { lineHeight: '1.6' }],
        'caption': ['0.875rem', { lineHeight: '1.4' }],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.5s ease-out',
        'glow-pulse': 'glowPulse 2s ease-in-out infinite',
        'float': 'float 3s ease-in-out infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        glowPulse: {
          '0%, 100%': { boxShadow: '0 0 20px rgba(45, 80, 22, 0.3)' },
          '50%': { boxShadow: '0 0 30px rgba(45, 80, 22, 0.6), 0 0 60px rgba(45, 80, 22, 0.3)' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
      },
      backdropBlur: {
        xs: '2px',
      },
      boxShadow: {
        'military': '0 0 20px rgba(45, 80, 22, 0.3)',
        'military-strong': '0 0 30px rgba(45, 80, 22, 0.5), 0 0 60px rgba(45, 80, 22, 0.2)',
        'inner-glow': 'inset 0 0 20px rgba(45, 80, 22, 0.2)',
      },
    },
  },
  plugins: [],
};

export default config;
